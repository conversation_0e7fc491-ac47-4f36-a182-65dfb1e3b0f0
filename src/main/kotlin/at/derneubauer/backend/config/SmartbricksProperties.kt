package at.derneubauer.backend.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.context.properties.bind.ConstructorBinding
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(SmartbricksProperties::class)
class SmartbricksConfiguration

@ConfigurationProperties(prefix = "neubauer.smartbricks")
data class SmartbricksProperties @ConstructorBinding constructor(
    val baseUrl: String,
    val apiBaseUrl: String,
    val defaultApiParams: DefaultApiParams
) {
    data class DefaultApiParams(
        val managerUserId: Long,
        val buildingObjectId: Long,
        val clientId: Long,
        val serviceCatalogId: Long,
    )
}
