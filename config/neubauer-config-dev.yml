neubauer:
  baseUrl: "https://mlo.developers.bytepoets.net:11080"
  dev:
    testSupportEndpointsActive: true

  filestore:
    s3Region: us-east-1
    s3Bucket: docker
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
  issue-image-filestore:
    s3Region: us-east-1
    s3Bucket: issue-img
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
    issuePrefixToRemove: AS
    issueScanFileExtension: .pdf
  issue-document-filestore:
    s3Region: us-east-1
    s3Bucket: issue-doc
    s3AccessKeyId: EKIEIOSFODNN7EXAMPLE
    s3SecretAccessKey: wJelrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
    s3Endpoint: http://filestore:9000
    pathStyleAccessEnabled: true
  offa:
    user: offa
    password: $2a$10$OnQRPFo8AvAeZ3nB4wkehOcwEm0i.kEtKMtVVX1/4503MEeS.WNiq
  sms:
    endpointUrl: https://api.websms.com/rest/
    accessToken: 1d4c4b56-597b-4096-90d6-d12830ea573f
  smartbricks:
    baseUrl: https://app.dev.smartbricks.at/
    apiBaseUrl: https://smartbricks-service-devbuild.azurewebsites.net/
    # these are the values that are required by the API for creating a project but we basically only have
    # the project number, therefore we have created the default values in the environments (dev & prod)
    defaultApiParams:
      managerUserId: 228
      buildingObjectId: 1158
      clientId: 1472
      # TODO: currently uses the "Smartbricks Leistungsbuch" but smartbricks will provide another
      # one which should be used instead at some point
      serviceCatalogId: 1

spring:
  datasource:
    url: **************************
    username: neubauer
    password: sikrit
    driverClassName: org.mariadb.jdbc.Driver

  security:
    oauth2:
      client:
        registration:
          smartbricks: # custom name, has to match provider's name
            provider: smartbricks
            client-id: PmXA9Sv93w7FtkGw4uloLVYFM4gc8pKT
            client-secret: aTLkjE7l5w0RRfxIsdBoOIWHyV5ft_bMahuST3aKKEg12Xf3K5lIPx6x2M1kenXN
            authorization-grant-type: authorization_code
            redirect-uri: "{baseUrl}/login/oauth2/code/{registrationId}"
            scope:
              - openid
              - profile
              - email
        provider:
          smartbricks: # custom name, has to match registration's provider name
            issuer-uri: https://auth.smartbricks.at/

  flyway:
    locations: classpath:db/migration,classpath:db/seed
    table: schema_version
    encoding: UTF-8

  web:
    resources:
      cache:
        cachecontrol:
          max-age: 31536000
      chain:
        cache: true
        strategy:
          content:
            enabled: true
            paths: /assets/**
      static-locations: classpath:/static/

  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB

  session:
    storeType: redis

  data:
    redis:
      host: redis
      port: 6379

  thymeleaf:
    cache: false

server:
  forward-headers-strategy: native

logging:
  level:
    org.springframework: INFO
    org.springframework.security: DEBUG
    at.derneubauer.backend: DEBUG
    at.derneubauer.backend.db: INFO
    at.derneubauer.backend.offa.db: INFO
